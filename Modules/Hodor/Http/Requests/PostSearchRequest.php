<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;

class PostSearchRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'has_tags'                      => '',
            'tag_related_id'                => '',
            'has_motifs'                    => '',
            'motif_related_id'              => '',
            'has_localised_motifs'          => '',
            'localised_motif_related_id'    => '',
            'published'                     => '',
            'featured'                      => '',
            'translation_title'             => '',
            'translated'                    => '',
        ];
    }
}
