<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;

class PostUpdateRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        foreach (array_keys(config('laravellocalization.supportedLocales')) as $langKey){
            $rules[$langKey] = [];
            if(
                empty($this->{$langKey . '.title'}) &&
                empty($this->{$langKey . '.content'}) &&
                empty($this->{$langKey . '.meta_title'}) &&
                empty($this->{$langKey . '.meta_description'})
            )
            {
                $this->merge([
                    $langKey => [],
                ]);
            }
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'featured'  => 'required',
            'published' => 'required',
            'tags'      => '',
            'motifs'    => 'required',
            'localisedMotifs' => 'required',
        ];

        foreach (array_keys(config('laravellocalization.supportedLocales')) as $langKey){
            $rules[$langKey . '.title']             = 'sometimes';
            $rules[$langKey . '.content']           = 'sometimes|required_with:' . $langKey . '.title';
            $rules[$langKey . '.meta_title']        = 'sometimes|required_with:' . $langKey . '.title';
            $rules[$langKey . '.meta_description']  = 'sometimes|required_with:' . $langKey . '.title';
        }
        return $rules;
    }
}
