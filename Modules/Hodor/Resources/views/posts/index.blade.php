@extends('hodor::layouts.master')

@section('content')
<section class="content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6"></div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="{{ route('hodor.posts.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Post</a>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="content">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Search Filters</h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body clearfix">
            <form method="GET" action="{{ route('hodor.posts.index') }}">
                {{-- Row 1: Title, Language --}}
                <div class="row">
                    <div class="form-group col-md-9">
                        <label>Title</label>
                        <input type="text" class="form-control" name="translation_title" value="{{ request('translation_title') }}"/>
                    </div>
                    <div class="form-group col-md-3">
                        <label>Language</label>
                        <select class="form-control custom-select" name="translated">
                            <option value="">All</option>
                            @foreach($languages as $locale => $language)
                                <option value="{!! $locale !!}" @if(request('translated') == $locale) selected @endif>
                                    {!! $language !!}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                {{-- Row 2: HasTags, Tag, Has Motifs, Motif --}}
                <div class="row">
                    <div class="form-group col-md-3">
                        <label>Has Tags</label>
                        <select class="form-control custom-select" name="has_tags">
                            <option value="">All</option>
                            <option value="yes" @if(request('has_tags') == 'yes') selected @endif>Yes</option>
                            <option value="no" @if(request('has_tags') == 'no') selected @endif>No</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3">
                        <label>Tag</label>
                        <select class="form-control custom-select" name="tag_related_id">
                            <option value="">All</option>
                            @foreach($tags as $id => $title)
                                <option value="{!! $id !!}" @if(request('tag_related_id') == $id) selected @endif>
                                    {!! $title !!}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group col-md-3">
                        <label>Has Motifs</label>
                        <select class="form-control custom-select" name="has_motifs">
                            <option value="">All</option>
                            <option value="yes" @if(request('has_motifs') == 'yes') selected @endif>Yes</option>
                            <option value="no" @if(request('has_motifs') == 'no') selected @endif>No</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3">
                        <label>Motif</label>
                        <select class="form-control custom-select" name="motif_related_id">
                            <option value="">All</option>
                            @foreach($motifs as $id => $title)
                                <option value="{!! $id !!}" @if(request('motif_related_id') == $id) selected @endif>
                                    {!! $title !!}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                {{-- Row 3: HasLocalisedMotifs, LocalisedMotif --}}
                <div class="row">
                    <div class="form-group col-md-3">
                        <label>Has LocalisedMotifs</label>
                        <select class="form-control custom-select" name="has_localised_motifs">
                            <option value="">All</option>
                            <option value="yes" @if(request('has_localised_motifs') == 'yes') selected @endif>Yes</option>
                            <option value="no" @if(request('has_localised_motifs') == 'no') selected @endif>No</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3">
                        <label>LocalisedMotif</label>
                        <select class="form-control custom-select" name="localised_motif_related_id">
                            <option value="">All</option>
                            @foreach($localisedMotifs as $id => $title)
                                <option value="{!! $id !!}" @if(request('localised_motif_related_id') == $id) selected @endif>
                                    {!! $title !!}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                {{-- Row 4: Published, Featured --}}
                <div class="row">
                    <div class="form-group col-md-3">
                        <label>Published</label>
                        <select class="form-control custom-select" name="published">
                            <option value="">All</option>
                            <option value="yes" @if(request('published') == 'yes') selected @endif>Yes</option>
                            <option value="no" @if(request('published') == 'no') selected @endif>No</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3">
                        <label>Featured</label>
                        <select class="form-control custom-select" name="featured">
                            <option value="">All</option>
                            <option value="yes" @if(request('featured') == 'yes') selected @endif>Yes</option>
                            <option value="no" @if(request('featured') == 'no') selected @endif>No</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <button type="submit" class="btn btn-default">SEARCH</button>
                    </div>
                    <div class="col-sm-6">
                        <div class="float-sm-right">
                            <a href="{{ route('hodor.posts.index') }}">Reset</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            @include('hodor::common.alert')
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div>{{ $posts->count() }} of {{ $posts->total() }} items</div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Lang</th>
                                    <th>Title</th>
                                    <th>Nr Media</th>
                                    <th>Tags</th>
                                    <th>Motifs</th>
                                    <th>LocalisedMotifs</th>
                                    <th>Published</th>
                                    <th>Featured</th>
                                    <th>Created</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                            @foreach ($posts as $post)
                                <tr>
                                    <td>
                                        <strong><a href="{{ route('hodor.posts.edit', $post->id) }}">{{ $post->id }}</a></strong>
                                        <div>{{ views($post)->count() . ' / ' . views($post)->period(CyrildeWit\EloquentViewable\Support\Period::pastWeeks(3))->count() }}</div>
                                    </td>
                                    <td>
                                        @foreach(array_keys($post->getTranslationsArray()) as $locale)
                                            <img src="{!! asset('images/' . $locale . '.png') !!}" />
                                        @endforeach
                                    </td>
                                    <td><strong><a href="{{ route('hodor.posts.edit', $post->id) }}">{{ $post->title }}</a></strong></td>
                                    <td>
                                        <a href="{{ route('hodor.posts.photos.index', $post->id) }}">{{ $post->getMedia('photos')->count() }}</a>
                                    </td>
                                    <td>@if($post->tags()->exists()) {{ $post->tags()->count() }} @else - @endif</td>
                                    <td>@if($post->motifs()->exists()) {{ $post->motifs()->count() }} @else - @endif</td>
                                    <td>@if($post->localisedMotifs()->exists()) {{ $post->localisedMotifs()->count() }} @else - @endif</td>
                                    <td>
                                        @if($post->published)
                                            <span class="badge bg-success">YES</span>
                                        @else
                                            <span class="badge bg-danger">NO</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($post->featured)
                                            <span class="badge bg-success">YES</span>
                                        @else
                                            <span class="badge bg-danger">NO</span>
                                        @endif
                                    </td>
                                    <td>{!! $post->created_at->format('d-m-Y H:i') !!}</td>
                                    <td class="text-right project-actions">
                                        <p>
                                            <a class="btn btn-outline-info" title="Edit" href="{{ route('hodor.posts.edit', $post->id) }}">
                                                <i class="fas fa-pen"></i>
                                            </a>
                                        </p>
                                        {!! Form::open(array('method' => 'DELETE', 'class' => '', 'route' => array('hodor.posts.destroy', $post->id))) !!}
                                            {!! Form::token() !!}
                                            <button class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this item?');"><i class="fas fa-trash"></i></button>
                                        {!! Form::close() !!}
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div>{{ $posts->links() }}</div>
</section>
@endsection
