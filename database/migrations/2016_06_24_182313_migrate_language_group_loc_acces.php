<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MigrateLanguageGroupLocAcces extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
    	// accessories_translations
    	DB::table('accessories_translations')->truncate();
    	DB::table('accessories_translations')->insert(["locale" => 'en', "accessory_id"	=> 1, "name" => 'Babyseat', "description"	=> 'Handy babyseat appropriate for babies from 1-15 kg.']);
    	DB::table('accessories_translations')->insert(["locale" => 'en', "accessory_id"	=> 2, "name" => 'GPS', "description"	=> 'Navigator in multiple languages.']);
    	DB::table('accessories_translations')->insert(["locale" => 'en', "accessory_id"	=> 3, "name" => 'Booster seat', "description"	=> 'Suitable for children over 15 kg.']);
    	DB::table('accessories_translations')->insert(["locale" => 'en', "accessory_id"	=> 4, "name" => 'Second Driver', "description"	=> 'Choose the option of having a second driver included in the rental agreement.']);
    	DB::table('accessories_translations')->insert(["locale" => 'el', "accessory_id"	=> 1, "name" => 'Παιδικό κάθισμα', "description"	=> 'Κατάλληλο για μωρά απο 1-15 kg. Διατίθεται δωρεάν.']);
    	DB::table('accessories_translations')->insert(["locale" => 'el', "accessory_id"	=> 2, "name" => 'GPS', "description"	=> 'Υποστήριξη πολλών γλωσσών.']);
    	DB::table('accessories_translations')->insert(["locale" => 'el', "accessory_id"	=> 3, "name" => 'Παιδικό κάθισμα', "description"	=> 'Κατάλληλο για παιδιά από 15 kg και άνω. Διατίθεται δωρεάν.']);
    	DB::table('accessories_translations')->insert(["locale" => 'el', "accessory_id"	=> 4, "name" => 'Δεύτερος οδηγός', "description"	=> 'Επιλογή προσθήκης δεύτερου οδηγού στο μισθωτήριο συμβόλαιο.']);
    	
    	// groups_translations
    	DB::table('groups_translations')->truncate();
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 1, "description" => 'Small', "seo_text" => '<p>Small cars are a great option for driving in the narrow streets of the traditional Cretan villages and for easily finding parking space in the bigger cities of Crete, while maintaining great fuel efficiency. We offer a wide variety of small rental cars, like the Citroen C1, the Peugeot 107, the Toyota Aygo and other similar models all in very low prices.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 2, "description" => 'Economy', "seo_text" => '<p>Looking for a reliable cheap car rental solution that offers good gas mileage? Then look no further! Our selection of economy cars, with models that include the Opel Corsa, the Skoda Fabia, the Nissan Micra and many more, will provide you with a decent vehicle in great rates.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 3, "description" => 'Compact', "seo_text" => '<p>If you are searching for a smaller car with increased safety and stability and fuel economy at the same time, then this category is for you. Our compact rental cars selection includes models like the Toyota Yaris, the Hyundai Accent and more all in great prices and with no deposit required.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 4, "description" => 'Standard', "seo_text" => '<p>If you want to comfortably fit four people for a longer trip then probably the smaller categories won&rsquo;t do the job. The standard category, however, offers this extra space you need along with a sporty look and extra features, while still maintaining the fuel consumptions at reasonable levels. The Opel Astra and the Hyundai i30 are the featured models of this category.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 5, "description" => 'Intermediate', "seo_text" => '<p>Looking to rent a four door car that can easily seat a family of five with a trunk that can fit multiple large luggage items? Then, the full size sedans offered in the intermediate category will be perfect for you. Models like the Ford Focus or the Kia Ceed will have all the features you are looking for.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 6, "description" => 'Large Minivan', "seo_text" => '<p>If the smaller minivans we offer do not fulfill your need for extra room you have the option to rent a large minivan. Whether it is for your big family or for a large group of friends a 9-seater minivan, such as the Opel Vivaro, the Fiat Scudo or the Nissan Primastar, will give you the extra space you need to fit everyone and everything in. Rent your minivan today and make this a journey to remember!</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 7, "description" => 'Luxury SUV', "seo_text" => '<p>Whether you are trying to impress someone or you want the flexibility with the luggage space and the additional horsepower, the luxury SUV category is for you. Rent a Hyundai Santa Fe or a similar premium SUV and get ready to explore the Cretan countryside.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 8, "description" => 'Minivan', "seo_text" => '<p>If you are looking for a car with roomy space on the back, so that the kids will have all the space they want or so that you can fit a group of multiple people then renting a minivan would be a great solution. We offer 7-seater minivans, like the Volkswaggen Caddy or the Fiat Doblo, which will provide you with all the extra room you need for multiple people, large luggage or even cargo.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 9, "description" => 'Intermediate SUV', "seo_text" => '<p>Looking forward to discover all the idyllic secluded beaches of Crete? Then an off-road 4x4 model, as the Hyundai Tucson, is what you need. We offer great quality SUVs to rent in affordable prices and with no hidden costs.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 10, "description" => 'Automatic', "seo_text" => '<p>Along with the other car categories we also offer the possibility to our customers to rent an automatic car. In this category the models of a Hyundai Accent and Volkswagen Polo, among others, are offered with automatic transmissions for a carefree driving experience during your visit in Crete.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 11, "description" => 'Convertible', "seo_text" => '<p>Would you like to feel the wind and Greek sun on your face, while you drive? Then a convertible is the right car for you to hire. Book a convertible car and enjoy the freedom and style that it offers.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 12, "description" => 'Hybrid', "seo_text" => '<p>Are you looking for maximum fuel efficiency and comfort at the same time? Rent a Honda Insight Hybrid or a similar green car from us and enjoy a carefree and eco-friendly vacation in Crete. Get ready to discover the Cretan Nature in the most nature-friendly way!</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'en', "group_id" => 13, "description" => 'Diesel', "seo_text" => '<p>Are you looking for maximum fuel efficiency and comfort at the same time? Rent a Citrean C4 or a similar diesel car from us and enjoy a carefree vacation in Crete. Get ready to discover the Cretan Nature in the most nature-friendly way!</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 1, "description" => 'Μίνι', "seo_text" => '<p>&Tau;&alpha; &mu;ί&nu;&iota; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&alpha; &epsilon;ί&nu;&alpha;&iota; &mu;&iota;&alpha; &pi;&omicron;&lambda;ύ &kappa;&alpha;&lambda;ή &epsilon;&pi;&iota;&lambda;&omicron;&gamma;ή &gamma;&iota;&alpha; &nu;&alpha; &epsilon;&xi;&epsilon;&rho;&epsilon;&upsilon;&nu;ή&sigma;&epsilon;&tau;&epsilon; &tau;&alpha; &sigma;&tau;&epsilon;&nu;ά &sigma;&omicron;&kappa;ά&kappa;&iota;&alpha; &tau;&omega;&nu; &pi;&alpha;&rho;&alpha;&delta;&omicron;&sigma;&iota;&alpha;&kappa;ώ&nu; &kappa;&rho;&eta;&tau;&iota;&kappa;ώ&nu; &chi;&omega;&rho;&iota;ώ&nu; &kappa;&alpha;&iota; &gamma;&iota;&alpha; &tau;&eta;&nu; &epsilon;ύ&kappa;&omicron;&lambda;&eta; &sigma;&tau;ά&theta;&mu;&epsilon;&upsilon;&sigma;&eta; &sigma;&tau;&iota;&sigmaf; &pi;ό&lambda;&epsilon;&iota;&sigmaf; &tau;&eta;&sigmaf; &Kappa;&rho;ή&tau;&eta;&sigmaf;, &epsilon;&nu;ώ &tau;&alpha;&upsilon;&tau;ό&chi;&rho;&omicron;&nu;&alpha; &delta;&iota;&alpha;&tau;&eta;&rho;&omicron;ύ&nu; &epsilon;&xi;&alpha;&iota;&rho;&epsilon;&tau;&iota;&kappa;ή &alpha;&pi;ό&delta;&omicron;&sigma;&eta; &kappa;&alpha;&upsilon;&sigma;ί&mu;&omega;&nu;. &Pi;&rho;&omicron;&sigma;&phi;έ&rho;&omicron;&upsilon;&mu;&epsilon; &mu;&iota;&alpha; &mu;&epsilon;&gamma;ά&lambda;&eta; &pi;&omicron;&iota;&kappa;&iota;&lambda;ί&alpha; &alpha;&pi;ό &mu;ί&nu;&iota; &epsilon;&nu;&omicron;&iota;&kappa;&iota;&alpha;&zeta;ό&mu;&epsilon;&nu;&alpha; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&alpha;, ό&pi;&omega;&sigmaf; &tau;&omicron; Citroen C1, &tau;&omicron; Peugeot 107, &tau;&omicron; Toyota Aygo &kappa;&alpha;&iota; ά&lambda;&lambda;&alpha; &pi;&alpha;&rho;ό&mu;&omicron;&iota;&alpha; &mu;&omicron;&nu;&tau;έ&lambda;&alpha;, ό&lambda;&alpha; &sigma;&epsilon; &pi;&omicron;&lambda;ύ &chi;&alpha;&mu;&eta;&lambda;έ&sigmaf; &tau;&iota;&mu;έ&sigmaf;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 2, "description" => 'Μικρό Οικονομικό', "seo_text" => '<p>&Psi;ά&chi;&nu;&epsilon;&tau;&epsilon; &gamma;&iota;&alpha; έ&nu;&alpha; &alpha;&xi;&iota;ό&pi;&iota;&sigma;&tau;&omicron; ό&chi;&eta;&mu;&alpha; &mu;&epsilon; &chi;&alpha;&mu;&eta;&lambda;ή &kappa;&alpha;&tau;&alpha;&nu;ά&lambda;&omega;&sigma;&eta; &kappa;&alpha;&upsilon;&sigma;ί&mu;&omega;&nu;; &Eta; &epsilon;&pi;&iota;&lambda;&omicron;&gamma;ή &mu;&alpha;&sigmaf; &alpha;&pi;ό &mu;&iota;&kappa;&rho;ά &omicron;&iota;&kappa;&omicron;&nu;&omicron;&mu;&iota;&kappa;ά &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&alpha;, &mu;&epsilon; &mu;&omicron;&nu;&tau;έ&lambda;&alpha; &pi;&omicron;&upsilon; &pi;&epsilon;&rho;&iota;&lambda;&alpha;&mu;&beta;ά&nu;&omicron;&upsilon;&nu; &tau;&omicron; Opel Corsa, &tau;&omicron; Skoda Fabia, &tau;&omicron; Nissan Micra &kappa;&alpha;&iota; &pi;&omicron;&lambda;&lambda;ά ά&lambda;&lambda;&alpha;, &theta;&alpha; &sigma;&alpha;&sigmaf; &delta;ώ&sigma;&epsilon;&iota; έ&nu;&alpha; &alpha;&xi;&iota;&omicron;&pi;&rho;&epsilon;&pi;έ&sigmaf; ό&chi;&eta;&mu;&alpha; &sigma;&epsilon; &pi;&omicron;&lambda;ύ &kappa;&alpha;&lambda;ή &tau;&iota;&mu;ή.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 3, "description" => 'Μεσαίο', "seo_text" => '<p>&Alpha;&nu; &psi;ά&chi;&nu;&epsilon;&tau;&epsilon; &gamma;&iota;&alpha; έ&nu;&alpha; &mu;&iota;&kappa;&rho;ό&tau;&epsilon;&rho;&omicron; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&omicron; &mu;&epsilon; &alpha;&upsilon;&xi;&eta;&mu;έ&nu;&eta; &alpha;&sigma;&phi;ά&lambda;&epsilon;&iota;&alpha; &kappa;&alpha;&iota; &sigma;&tau;&alpha;&theta;&epsilon;&rho;ό&tau;&eta;&tau;&alpha; &kappa;&alpha;&iota; &tau;&alpha;&upsilon;&tau;ό&chi;&rho;&omicron;&nu;&alpha; &omicron;&iota;&kappa;&omicron;&nu;&omicron;&mu;ί&alpha; &kappa;&alpha;&upsilon;&sigma;ί&mu;&omega;&nu;, &tau;ό&tau;&epsilon; &eta; &mu;&epsilon;&sigma;&alpha;ί&alpha; &kappa;&alpha;&tau;&eta;&gamma;&omicron;&rho;ί&alpha; &epsilon;ί&nu;&alpha;&iota; &gamma;&iota;&alpha; &sigma;&alpha;&sigmaf;. &Eta; &epsilon;&pi;&iota;&lambda;&omicron;&gamma;ή &epsilon;&nu;&omicron;&iota;&kappa;ί&alpha;&sigma;&eta;&sigmaf; &alpha;&upsilon;&tau;&omicron;&kappa;&iota;&nu;ή&tau;&omega;&nu; &sigma;&tau;&eta; &mu;&epsilon;&sigma;&alpha;ί&alpha; &kappa;&alpha;&tau;&eta;&gamma;&omicron;&rho;ί&alpha; &pi;&epsilon;&rho;&iota;&lambda;&alpha;&mu;&beta;ά&nu;&epsilon;&iota; &mu;&omicron;&nu;&tau;έ&lambda;&alpha; ό&pi;&omega;&sigmaf; &tau;&omicron; Toyota Yaris, &tau;&omicron; Hyundai Accent &kappa;&alpha;&iota; ά&lambda;&lambda;&alpha;, ό&lambda;&alpha; &sigma;&epsilon; &pi;&omicron;&lambda;ύ &kappa;&alpha;&lambda;έ&sigmaf; &tau;&iota;&mu;έ&sigmaf; &kappa;&alpha;&iota; &chi;&omega;&rho;ί&sigmaf; &nu;&alpha; &alpha;&pi;&alpha;&iota;&tau;&epsilon;ί&tau;&alpha;&iota; &kappa;&alpha;&tau;ά&theta;&epsilon;&sigma;&eta; ή &chi;&rho;ή&sigma;&eta; &pi;&iota;&sigma;&tau;&omega;&tau;&iota;&kappa;ή&sigmaf; &kappa;&alpha;&tau;ά &tau;&eta;&nu; &kappa;&rho;ά&tau;&eta;&sigma;&eta;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 4, "description" => 'Standard', "seo_text" => '<p>&Alpha;&nu; &theta;έ&lambda;&epsilon;&tau;&epsilon; έ&nu;&alpha; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&omicron; &pi;&omicron;&upsilon; &nu;&alpha; &chi;&omega;&rho;ά&epsilon;&iota; ά&nu;&epsilon;&tau;&alpha; &tau;έ&sigma;&sigma;&epsilon;&rho;&alpha; ά&tau;&omicron;&mu;&alpha; &gamma;&iota;&alpha; έ&nu;&alpha; &mu;&epsilon;&gamma;&alpha;&lambda;ύ&tau;&epsilon;&rho;&omicron; &tau;&alpha;&xi;ί&delta;&iota;,, &tau;ό&tau;&epsilon; &epsilon;ί&nu;&alpha;&iota; &pi;&iota;&theta;&alpha;&nu;ό ό&tau;&iota; &omicron;&iota; &mu;&iota;&kappa;&rho;ό&tau;&epsilon;&rho;&epsilon;&sigmaf; &kappa;&alpha;&tau;&eta;&gamma;&omicron;&rho;ί&epsilon;&sigmaf; &delta;&epsilon;&nu; &theta;&alpha; &sigma;&alpha;&sigmaf; &iota;&kappa;&alpha;&nu;&omicron;&pi;&omicron;&iota;ή&sigma;&omicron;&upsilon;&nu;. &Eta; &kappa;&alpha;&tau;&eta;&gamma;&omicron;&rho;ί&alpha; standard, &omega;&sigma;&tau;ό&sigma;&omicron;, &pi;&rho;&omicron;&sigma;&phi;έ&rho;&epsilon;&iota; &tau;&omicron;&nu; &epsilon;&pi;&iota;&pi;&lambda;έ&omicron;&nu; &chi;ώ&rho;&omicron; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&sigma;&tau;&epsilon; &mu;&alpha;&zeta;ί &mu;&epsilon; &mu;&iota;&alpha; &sigma;&pi;&omicron;&rho; &epsilon;&mu;&phi;ά&nu;&iota;&sigma;&eta; &kappa;&alpha;&iota; &epsilon;&pi;&iota;&pi;&lambda;έ&omicron;&nu; &alpha;&nu;έ&sigma;&epsilon;&iota;&sigmaf;, &delta;&iota;&alpha;&tau;&eta;&rho;ώ&nu;&tau;&alpha;&sigmaf; &pi;&alpha;&rho;ά&lambda;&lambda;&eta;&lambda;&alpha; &tau;&eta;&nu; &kappa;&alpha;&tau;&alpha;&nu;ά&lambda;&omega;&sigma;&eta; &kappa;&alpha;&upsilon;&sigma;ί&mu;&omega;&nu; &sigma;&epsilon; &lambda;&omicron;&gamma;&iota;&kappa;ά &epsilon;&pi;ί&pi;&epsilon;&delta;&alpha;. &Tau;&omicron; Opel Astra &kappa;&alpha;&iota; &tau;&omicron; i30 &tau;&eta;&sigmaf; Hyundai &epsilon;ί&nu;&alpha;&iota; &omicron;&iota; &chi;&alpha;&rho;&alpha;&kappa;&tau;ή&rho;&iota;&sigma;&epsilon; &tau;&alpha; &mu;&omicron;&nu;&tau;έ&lambda;&alpha; &alpha;&upsilon;&tau;ή&sigmaf; &tau;&eta;&sigmaf; &kappa;&alpha;&tau;&eta;&gamma;&omicron;&rho;ί&alpha;&sigmaf;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 5, "description" => 'Intermediate', "seo_text" => '<p>&Psi;ά&chi;&nu;&epsilon;&tau;&epsilon; &nu;&alpha; &nu;&omicron;&iota;&kappa;&iota;ά&sigma;&epsilon;&tau;&epsilon; έ&nu;&alpha; &tau;&epsilon;&tau;&rho;ά&theta;&upsilon;&rho;&omicron; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&omicron; &pi;&omicron;&upsilon; &mu;&pi;&omicron;&rho;&epsilon;ί &epsilon;ύ&kappa;&omicron;&lambda;&alpha; &nu;&alpha; &chi;&omega;&rho;έ&sigma;&epsilon;&iota; &mu;&iota;&alpha; &omicron;&iota;&kappa;&omicron;&gamma;έ&nu;&epsilon;&iota;&alpha; &tau;&omega;&nu; &pi;έ&nu;&tau;&epsilon; &kappa;&alpha;&iota; &mu;&epsilon; έ&nu;&alpha;&nu; &mu;&epsilon;&gamma;ά&lambda;&omicron; &chi;ώ&rho;&omicron; &gamma;&iota;&alpha; &alpha;&pi;&omicron;&sigma;&kappa;&epsilon;&upsilon;έ&sigmaf;; &Tau;ό&tau;&epsilon;, &tau;&alpha; &omicron;&chi;ή&mu;&alpha;&tau;&alpha; &pi;&omicron;&upsilon; &pi;&rho;&omicron;&sigma;&phi;έ&rho;&omicron;&nu;&tau;&alpha;&iota; &sigma;&tau;&eta;&nu; &epsilon;&nu;&delta;&iota;ά&mu;&epsilon;&sigma;&eta; &kappa;&alpha;&tau;&eta;&gamma;&omicron;&rho;ί&alpha; &theta;&alpha; &epsilon;ί&nu;&alpha;&iota; &tau;έ&lambda;&epsilon;&iota;&alpha; &gamma;&iota;&alpha; &epsilon;&sigma;ά&sigmaf;. &Mu;&omicron;&nu;&tau;έ&lambda;&alpha; ό&pi;&omega;&sigmaf; &tau;&omicron; Ford Focus ή &tau;&omicron; Kia Ceed &delta;&iota;&alpha;&theta;έ&tau;&omicron;&upsilon;&nu; ό&lambda;&alpha; &tau;&alpha; &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά &pi;&omicron;&upsilon; &psi;ά&chi;&nu;&epsilon;&tau;&epsilon;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 6, "description" => 'Εννιαθέσιο', "seo_text" => '<p>&Alpha;&nu; &tau;&alpha; <a href="http://rentcars-crete.com/cars?group=8">&mu;&iota;&kappa;&rho;ό&tau;&epsilon;&rho;&alpha; &mu;ί&nu;&iota; &beta;&alpha;&nu;</a> &pi;&omicron;&upsilon; &pi;&rho;&omicron;&sigma;&phi;έ&rho;&omicron;&upsilon;&mu;&epsilon; &delta;&epsilon;&nu; &pi;&lambda;&eta;&rho;&omicron;ύ&nu; ό&lambda;&epsilon;&sigmaf; &sigma;&alpha;&sigmaf; &tau;&iota;&sigmaf; &alpha;&nu;ά&gamma;&kappa;&epsilon;&sigmaf; &gamma;&iota;&alpha; &epsilon;&pi;&iota;&pi;&lambda;έ&omicron;&nu; &chi;ώ&rho;&omicron; &tau;ό&tau;&epsilon; έ&chi;&epsilon;&tau;&epsilon; &tau;&eta; &delta;&upsilon;&nu;&alpha;&tau;ό&tau;&eta;&tau;&alpha; &nu;&alpha; &nu;&omicron;&iota;&kappa;&iota;ά&sigma;&epsilon;&tau;&epsilon; έ&nu;&alpha; &epsilon;&nu;&nu;&iota;&alpha;&theta;έ&sigma;&iota;&omicron;&nbsp;&mu;ί&nu;&iota; &beta;&alpha;&nu;. &Epsilon;ί&tau;&epsilon; &pi;&rho;ό&kappa;&epsilon;&iota;&tau;&alpha;&iota; &gamma;&iota;&alpha; &mu;&epsilon;&gamma;ά&lambda;&eta; &omicron;&iota;&kappa;&omicron;&gamma;έ&nu;&epsilon;&iota;&alpha; ή &gamma;&iota;&alpha; &mu;&iota;&alpha; &mu;&epsilon;&gamma;ά&lambda;&eta; &pi;&alpha;&rho;έ&alpha; &phi;ί&lambda;&omega;&nu; έ&nu;&alpha; 9-&theta;έ&sigma;&iota;&omicron; minivan, ό&pi;&omega;&sigmaf; &tau;&omicron; Opel Vivaro, &tau;&omicron; Fiat Scudo ή &tau;&omicron; Nissan Primastar, &theta;&alpha; &sigma;&alpha;&sigmaf; &delta;ώ&sigma;&epsilon;&iota; &tau;&omicron;&nu; &epsilon;&pi;&iota;&pi;&lambda;έ&omicron;&nu; &chi;ώ&rho;&omicron; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&sigma;&tau;&epsilon; &gamma;&iota;&alpha; &nu;&alpha; &chi;&omega;&rho;έ&sigma;&epsilon;&iota; &tau;&omicron;&upsilon;&sigmaf; &pi;ά&nu;&tau;&epsilon;&sigmaf; &kappa;&alpha;&iota; &tau;&alpha; &pi;ά&nu;&tau;&alpha;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 7, "description" => 'Πολυτελές Τζίπ', "seo_text" => '<p>&Epsilon;ί&tau;&epsilon; &pi;&rho;&omicron;&sigma;&pi;&alpha;&theta;&epsilon;ί&tau;&epsilon; &nu;&alpha; &epsilon;&nu;&tau;&upsilon;&pi;&omega;&sigma;&iota;ά&sigma;&epsilon;&tau;&epsilon; &kappa;ά&pi;&omicron;&iota;&omicron;&nu; &epsilon;ί&tau;&epsilon; &theta;έ&lambda;&epsilon;&tau;&epsilon; &epsilon;&upsilon;&epsilon;&lambda;&iota;&xi;ί&alpha; &mu;&epsilon; &tau;&omicron; &chi;ώ&rho;&omicron; &tau;&omega;&nu; &alpha;&pi;&omicron;&sigma;&kappa;&epsilon;&upsilon;ώ&nu; &kappa;&alpha;&iota; &tau;&eta;&nu; &epsilon;&pi;&iota;&pi;&lambda;έ&omicron;&nu; &iota;&pi;&pi;&omicron;&delta;ύ&nu;&alpha;&mu;&eta;, &eta; &kappa;&alpha;&tau;&eta;&gamma;&omicron;&rho;ί&alpha; &pi;&omicron;&lambda;&upsilon;&tau;&epsilon;&lambda;ώ&nu; &tau;&zeta;&iota;&pi; &epsilon;ί&nu;&alpha;&iota; &gamma;&iota;&alpha; &sigma;&alpha;&sigmaf;. &Nu;&omicron;&iota;&kappa;&iota;ά&sigma;&tau;&epsilon; έ&nu;&alpha; Hyundai Santa Fe ή έ&nu;&alpha; &pi;&alpha;&rho;ό&mu;&omicron;&iota;&omicron; &pi;&omicron;&lambda;&upsilon;&tau;&epsilon;&lambda;έ&sigmaf; &tau;&zeta;&iota;&pi; &kappa;&alpha;&iota; &epsilon;&tau;&omicron;&iota;&mu;&alpha;&sigma;&tau;&epsilon;ί&tau;&epsilon; &nu;&alpha; &epsilon;&xi;&epsilon;&rho;&epsilon;&upsilon;&nu;ή&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &kappa;&rho;&eta;&tau;&iota;&kappa;ή ύ&pi;&alpha;&iota;&theta;&rho;&omicron;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 8, "description" => 'Επταθέσιο', "seo_text" => '<p>&Alpha;&nu; &psi;ά&chi;&nu;&epsilon;&tau;&epsilon; &gamma;&iota;&alpha; έ&nu;&alpha; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&omicron; &mu;&epsilon; &pi;&omicron;&lambda;ύ &chi;ώ&rho;&omicron; &sigma;&tau;&omicron; &pi;ί&sigma;&omega; &mu;έ&rho;&omicron;&sigmaf;, έ&tau;&sigma;&iota; ώ&sigma;&tau;&epsilon; &tau;&alpha; &pi;&alpha;&iota;&delta;&iota;ά &theta;&alpha; έ&chi;&omicron;&upsilon;&nu; ό&lambda;&eta; &tau;&eta;&nu; ά&nu;&epsilon;&sigma;&eta; &pi;&omicron;&upsilon; &theta;έ&lambda;&omicron;&upsilon;&nu; ή έ&tau;&sigma;&iota; ώ&sigma;&tau;&epsilon; &nu;&alpha; &mu;&pi;&omicron;&rho;&epsilon;ί &nu;&alpha; &chi;&omega;&rho;έ&sigma;&epsilon;&iota; &sigma;&epsilon; &mu;&iota;&alpha; &mu;&epsilon;&gamma;ά&lambda;&eta; &pi;&alpha;&rho;έ&alpha; &tau;ό&tau;&epsilon; &eta; &epsilon;&nu;&omicron;&iota;&kappa;ί&alpha;&sigma;&eta; &epsilon;&nu;ό&sigmaf; &epsilon;&pi;&tau;&alpha;&theta;έ&sigma;&iota;&omicron;&upsilon; &mu;ί&nu;&iota; &beta;&alpha;&nu; &theta;&alpha; ή&tau;&alpha;&nu; &mu;&iota;&alpha; &pi;&omicron;&lambda;ύ &kappa;&alpha;&lambda;ή &lambda;ύ&sigma;&eta;. &Sigma;&alpha;&sigmaf; &pi;&rho;&omicron;&sigma;&phi;έ&rho;&omicron;&upsilon;&mu;&epsilon; 7-&theta;έ&sigma;&iota;&alpha; &mu;ί&nu;&iota; &beta;&alpha;&nu;, ό&pi;&omega;&sigmaf; &tau;&omicron; Volkswaggen Caddy ή &tau;&omicron; Fiat Doblo, &tau;&alpha; &omicron;&pi;&omicron;ί&alpha; &theta;&alpha; &sigma;&alpha;&sigmaf; &delta;ώ&sigma;&omicron;&upsilon;&nu; &tau;&omicron;&nu; &epsilon;&pi;&iota;&pi;&lambda;έ&omicron;&nu; &chi;ώ&rho;&omicron; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&sigma;&tau;&epsilon; &gamma;&iota;&alpha; &pi;&omicron;&lambda;&lambda;ά ά&tau;&omicron;&mu;&alpha;, &alpha;&pi;&omicron;&sigma;&kappa;&epsilon;&upsilon;έ&sigmaf; ή &alpha;&kappa;ό&mu;&eta; &kappa;&alpha;&iota; ά&lambda;&lambda;&omicron;&upsilon; &epsilon;ί&delta;&omicron;&upsilon;&sigmaf; &phi;&omicron;&rho;&tau;ί&alpha;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 9, "description" => 'Μεγάλο Τζιπ', "seo_text" => '<p>&Alpha;&nu;&upsilon;&pi;&omicron;&mu;&omicron;&nu;&epsilon;ί&tau;&epsilon; &nu;&alpha; &alpha;&nu;&alpha;&kappa;&alpha;&lambda;ύ&psi;&epsilon;&tau;&epsilon; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &epsilon;&iota;&delta;&upsilon;&lambda;&lambda;&iota;&alpha;&kappa;έ&sigmaf; &alpha;&pi;ό&mu;&epsilon;&rho;&epsilon;&sigmaf; &pi;&alpha;&rho;&alpha;&lambda;ί&epsilon;&sigmaf; &tau;&eta;&sigmaf; &Kappa;&rho;ή&tau;&eta;&sigmaf;; &Tau;ό&tau;&epsilon;, έ&nu;&alpha; &mu;&epsilon;&gamma;ά&lambda;&omicron; &tau;&zeta;&iota;&pi;, ό&pi;&omega;&sigmaf; &tau;&omicron; Hyundai Tucson, &epsilon;ί&nu;&alpha;&iota; ό, &tau;&iota; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&sigma;&tau;&epsilon;. &Pi;&rho;&omicron;&sigma;&phi;έ&rho;&omicron;&upsilon;&mu;&epsilon; &upsilon;&psi;&eta;&lambda;ή&sigmaf; &pi;&omicron;&iota;ό&tau;&eta;&tau;&alpha;&sigmaf; &mu;&epsilon;&gamma;ά&lambda;&alpha; &tau;&zeta;&iota;&pi; &pi;&rho;&omicron;&sigmaf; &epsilon;&nu;&omicron;&iota;&kappa;ί&alpha;&sigma;&eta; &sigma;&epsilon; &pi;&rho;&omicron;&sigma;&iota;&tau;έ&sigmaf; &tau;&iota;&mu;έ&sigmaf; &kappa;&alpha;&iota; &chi;&omega;&rho;ί&sigmaf; &kappa;&rho;&upsilon;&phi;έ&sigmaf; &chi;&rho;&epsilon;ώ&sigma;&epsilon;&iota;&sigmaf;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 10, "description" => 'Αυτόματο', "seo_text" => '<p>&Pi;&rho;&omicron;&sigma;&phi;έ&rho;&omicron;&upsilon;&mu;&epsilon; &tau;&eta; &delta;&upsilon;&nu;&alpha;&tau;ό&tau;&eta;&tau;&alpha; &sigma;&tau;&omicron;&upsilon;&sigmaf; &pi;&epsilon;&lambda;ά&tau;&epsilon;&sigmaf; &mu;&alpha;&sigmaf; &gamma;&iota;&alpha; &epsilon;&nu;&omicron;&iota;&kappa;ί&alpha;&sigma;&eta; &alpha;&upsilon;&tau;ό&mu;&alpha;&tau;&omicron;&upsilon; &alpha;&upsilon;&tau;&omicron;&kappa;&iota;&nu;ή&tau;&omicron;&upsilon;. &Sigma;&epsilon; &alpha;&upsilon;&tau;ή &tau;&eta;&nu; &kappa;&alpha;&tau;&eta;&gamma;&omicron;&rho;ί&alpha;, &tau;&alpha; &mu;&omicron;&nu;&tau;έ&lambda;&alpha; Hyundai Accent &kappa;&alpha;&iota; Volkswagen Polo, &mu;&epsilon;&tau;&alpha;&xi;ύ ά&lambda;&lambda;&omega;&nu;, &pi;&rho;&omicron;&sigma;&phi;έ&rho;&omicron;&nu;&tau;&alpha;&iota; &mu;&epsilon; &alpha;&upsilon;&tau;ό&mu;&alpha;&tau;&omicron; &kappa;&iota;&beta;ώ&tau;&iota;&omicron; &tau;&alpha;&chi;&upsilon;&tau;ή&tau;&omega;&nu; &gamma;&iota;&alpha; &mu;&iota;&alpha; &xi;έ&gamma;&nu;&omicron;&iota;&alpha;&sigma;&tau;&eta; &omicron;&delta;&eta;&gamma;&iota;&kappa;ή &epsilon;&mu;&pi;&epsilon;&iota;&rho;ί&alpha; &kappa;&alpha;&tau;ά &tau;&eta;&nu; &epsilon;&pi;ί&sigma;&kappa;&epsilon;&psi;ή &sigma;&alpha;&sigmaf; &sigma;&tau;&eta;&nu; &Kappa;&rho;ή&tau;&eta;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 11, "description" => 'Κάμπριο', "seo_text" => '<p>&Theta;&alpha; &theta;έ&lambda;&alpha;&tau;&epsilon; &nu;&alpha; &alpha;&iota;&sigma;&theta;ά&nu;&epsilon;&sigma;&tau;&epsilon;&nbsp;&tau;&omicron;&nu; ά&nu;&epsilon;&mu;&omicron; &kappa;&alpha;&iota; &tau;&omicron;&nu; ή&lambda;&iota;&omicron; &sigma;&tau;&omicron; &pi;&rho;ό&sigma;&omega;&pi;ό &sigma;&alpha;&sigmaf;, &epsilon;&nu;ώ &omicron;&delta;&eta;&gamma;&epsilon;ί&tau;&epsilon;; &Tau;ό&tau;&epsilon; &eta; &epsilon;&nu;&omicron;&iota;&kappa;ί&alpha;&sigma;&eta; &epsilon;&nu;ό&sigmaf; &kappa;ά&mu;&pi;&rho;&iota;&omicron; &alpha;&upsilon;&tau;&omicron;&kappa;&iota;&nu;ή&tau;&omicron;&upsilon;&nbsp;&epsilon;ί&nu;&alpha;&iota; &eta; &kappa;&alpha;&lambda;ύ&tau;&epsilon;&rho;&eta; &epsilon;&pi;&iota;&lambda;&omicron;&gamma;ή &gamma;&iota;&alpha; &epsilon;&sigma;ά&sigmaf;. &Kappa;ά&nu;&tau;&epsilon; &kappa;&rho;ά&tau;&eta;&sigma;&eta; &gamma;&iota;&alpha; έ&nu;&alpha; &kappa;ά&mu;&pi;&rho;&iota;&omicron; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&omicron; &kappa;&alpha;&iota; &alpha;&pi;&omicron;&lambda;&alpha;ύ&sigma;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&lambda;&epsilon;&upsilon;&theta;&epsilon;&rho;ί&alpha; &kappa;&alpha;&iota; &tau;&omicron; &sigma;&tau;&upsilon;&lambda; &pi;&omicron;&upsilon; &pi;&rho;&omicron;&sigma;&phi;έ&rho;&epsilon;&iota;.</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 12, "description" => 'Υβριδικό', "seo_text" => '<p>&Psi;ά&chi;&nu;&epsilon;&tau;&epsilon; &nu;&alpha; &nu;&omicron;&iota;&kappa;&iota;ά&sigma;&epsilon;&tau;&epsilon; έ&nu;&alpha; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&omicron; &pi;&omicron;&upsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&phi;έ&rho;&epsilon;&iota; &tau;&eta; &mu;έ&gamma;&iota;&sigma;&tau;&eta; &delta;&upsilon;&nu;&alpha;&tau;ή &alpha;&pi;ό&delta;&omicron;&sigma;&eta; &kappa;&alpha;&upsilon;&sigma;ί&mu;&omega;&nu; &kappa;&alpha;&iota; ά&nu;&epsilon;&sigma;&eta; &tau;&eta;&nu; ί&delta;&iota;&alpha; &sigma;&tau;&iota;&gamma;&mu;ή; &Nu;&omicron;&iota;&kappa;&iota;ά&sigma;&tau;&epsilon; έ&nu;&alpha; Hybrid Honda Insight ή &kappa;ά&pi;&omicron;&iota;&omicron; &pi;&alpha;&rho;ό&mu;&omicron;&iota;&omicron; &upsilon;&beta;&rho;&iota;&delta;&iota;&kappa;ό &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&omicron; &alpha;&pi;ό &epsilon;&mu;ά&sigmaf; &kappa;&alpha;&iota; &nu;&alpha; &alpha;&pi;&omicron;&lambda;&alpha;ύ&sigma;&epsilon;&tau;&epsilon; &xi;έ&gamma;&nu;&omicron;&iota;&alpha;&sigma;&tau;&epsilon;&sigmaf; &kappa;&alpha;&iota; &phi;&iota;&lambda;&iota;&kappa;έ&sigmaf; &pi;&rho;&omicron;&sigmaf; &tau;&omicron; &pi;&epsilon;&rho;&iota;&beta;ά&lambda;&lambda;&omicron;&nu; &delta;&iota;&alpha;&kappa;&omicron;&pi;έ&sigmaf; &sigma;&tau;&eta;&nu; &Kappa;&rho;ή&tau;&eta;. &Epsilon;&tau;&omicron;&iota;&mu;&alpha;&sigma;&tau;&epsilon;ί&tau;&epsilon; &nu;&alpha; &alpha;&nu;&alpha;&kappa;&alpha;&lambda;ύ&psi;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &kappa;&rho;&eta;&tau;&iota;&kappa;ή &phi;ύ&sigma;&eta; &mu;&epsilon; &tau;&omicron;&nu; &pi;&iota;&omicron; &phi;&iota;&lambda;&iota;&kappa;ό &pi;&rho;&omicron;&sigmaf; &tau;&eta; &phi;ύ&sigma;&eta; &tau;&rho;ό&pi;&omicron;!</p>\r\n']);
    	DB::table('groups_translations')->insert(["locale" => 'el', "group_id" => 13, "description" => 'Πετρέλαιο', "seo_text" => '<p>&Beta;&rho;&epsilon;ί&tau;&epsilon; &tau;&omicron; &iota;&delta;&alpha;&nu;&iota;&kappa;ό &epsilon;&nu;&omicron;&iota;&kappa;&iota;&alpha;&zeta;ό&mu;&epsilon;&nu;&omicron; &alpha;&upsilon;&tau;&omicron;&kappa;ί&nu;&eta;&tau;&omicron; &gamma;&iota;&alpha; &tau;&iota;&sigmaf; &delta;&iota;&alpha;&kappa;&omicron;&pi;έ&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&tau;&omicron; &Eta;&rho;ά&kappa;&lambda;&epsilon;&iota;&omicron;, &sigma;&tau;&alpha; &Chi;&alpha;&nu;&iota;ά, &sigma;&tau;&eta;&nu; &Epsilon;&lambda;&omicron;ύ&nu;&tau;&alpha;, &sigma;&tau;&omicron; &Rho;έ&theta;&upsilon;&mu;&nu;&omicron;. &Epsilon;&pi;&iota;&lambda;έ&xi;&tau;&epsilon; &mu;ί&alpha; &alpha;&pi;ό &tau;&iota;&sigmaf; &pi;&rho;&omicron;&sigma;&phi;&omicron;&rho;έ&sigmaf; &epsilon;&nu;&omicron;&iota;&kappa;ί&alpha;&sigma;&eta;&sigmaf; &alpha;&upsilon;&tau;&omicron;&kappa;&iota;&nu;ή&tau;&omega;&nu; &sigma;&tau;&eta;&nu; &Kappa;&rho;ή&tau;&eta; &tau;&eta;&sigmaf; &epsilon;&tau;&alpha;&iota;&rho;&epsilon;ί&alpha;&sigmaf; &mu;&alpha;&sigmaf; &kappa;&alpha;&iota; &alpha;&pi;&omicron;&lambda;&alpha;ύ&sigma;&tau;&epsilon; &tau;&iota;&sigmaf; &omicron;&mu;&omicron;&rho;&phi;&iota;έ&sigmaf; &tau;&eta;&sigmaf; &Kappa;&rho;ή&tau;&eta;&sigmaf; ό&lambda;&omicron; &tau;&omicron; έ&tau;&omicron;&sigmaf;.</p>\r\n']);
    	 
    	// locations_translations
    	DB::table('locations_translations')->truncate();
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 1, "name" => 'Heraklion airport']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 2, "name" => 'Heraklion port']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 3, "name" => 'Heraklion downtown']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 4, "name" => 'Heraklion Agia Pelagia']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 5, "name" => 'Heraklion Ammoudara']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 6, "name" => 'Heraklion Analipsi']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 7, "name" => 'Heraklion Annissaras']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 8, "name" => 'Heraklion Chersonisos']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 9, "name" => 'Heraklion Fodele']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 10, "name" => 'Heraklion Gournes']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 11, "name" => 'Heraklion Gouves']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 12, "name" => 'Heraklion Kokkini Hani']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 13, "name" => 'Heraklion Koutouloufari']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 14, "name" => 'Heraklion Lygaria']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 15, "name" => 'Heraklion Malia']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 16, "name" => 'Heraklion Piskopiano']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 17, "name" => 'Heraklion Stalis']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 18, "name" => 'Chania airport']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 19, "name" => 'Chania port']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 20, "name" => 'Chania downtown']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 21, "name" => 'Chania Agia Marina']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 22, "name" => 'Chania Kolimpari']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 23, "name" => 'Chania Platanias']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 24, "name" => 'Rethymno port']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 25, "name" => 'Rethymno downtown']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 26, "name" => 'Rethymno Bali']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 27, "name" => 'Rethymno Georgioupolis']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 28, "name" => 'Rethymno Kavros']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 29, "name" => 'Rethymno Panormo']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 30, "name" => 'Rethymno Platanias']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 31, "name" => 'Rethymno Scaleta']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 32, "name" => 'Lassithi Agios Nikolaos port']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 33, "name" => 'Lassithi Agios Nikolaos downtown']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 34, "name" => 'Lassithi Elounda']);
    	DB::table('locations_translations')->insert(["locale" => 'en', "location_id" => 35, "name" => 'Lassithi Sissi']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 30, "name" => 'Ρέθυμνο Πλατανιάς']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 1, "name" => 'Ηράκλειο Αεροδρόμιο']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 2, "name" => 'Ηράκλειο Λιμάνι']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 3, "name" => 'Ηράκλειο (Κέντρο)']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 4, "name" => 'Ηράκλειο Αγία Πελαγία']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 5, "name" => 'Ηράκλειο Αμμουδάρα']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 6, "name" => 'Ηράκλειο Ανάληψη']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 7, "name" => 'Ηράκλειο Ανισσαράς']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 8, "name" => 'Ηράκλειο Χερσόνησος']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 9, "name" => 'Ηράκλειο Φόδελε']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 10, "name" => 'Ηράκλειο Γούρνες']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 11, "name" => 'Ηράκλειο Γούβες']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 12, "name" => 'Ηράκλειο Κοκκίνη Χάνι']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 13, "name" => 'Ηράκλειο Κουτουλουφάρι']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 14, "name" => 'Ηράκλειο Λυγαριά']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 15, "name" => 'Ηράκλειο Μάλια']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 16, "name" => 'Ηράκλειο Πισκοπιανό']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 17, "name" => 'Ηράκλειο Σταλίδα']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 18, "name" => 'Χανιά Αεροδρόμιο']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 19, "name" => 'Χανιά Λιμάνι']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 20, "name" => 'Χανιά (Κέντρο)']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 21, "name" => 'Χανιά Αγία Μαρίνα']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 22, "name" => 'Χανιά Κολυμπάρι']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 23, "name" => 'Χανιά Πλατανιάς']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 24, "name" => 'Ρέθυμνο Λιμάνι']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 26, "name" => 'Ρέθυμνο Μπαλί']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 27, "name" => 'Ρέθυμνο Γεωργιούπολη']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 28, "name" => 'Ρέθυμνο Καβρός']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 29, "name" => 'Ρέθυμνο Πάνορμο']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 31, "name" => 'Ρέθυμνο Σκαλέτα']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 32, "name" => 'Λασίθι Άγιος Νικόλαος Λιμάνι']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 33, "name" => 'Λασίθι Άγιος Νικόλαος (Κέντρο)']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 34, "name" => 'Λασίθι Ελούντα']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 35, "name" => 'Λασίθι Σίσι']);
    	DB::table('locations_translations')->insert(["locale" => 'el', "location_id" => 25, "name" => 'Ρέθυμνο (Κέντρο)']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
