<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * Seeder to connect Posts to LocalisedMotifs based on content analysis
 * 
 * This seeder was generated by analyzing post content and determining
 * the most relevant LocalisedMotifs for each post using LLM analysis.
 * 
 * LocalisedMotifs reference:
 * 1: Heraklion
 * 2: Heraklion Airport
 * 3: Chania
 * 4: Chania Airport
 * 5: Rethymno
 * 6: Agios Nikolaos
 */
class PostLocalisedMotifsConnectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Connecting posts to LocalisedMotifs based on content analysis...');

        // Clear existing connections
        DB::table('localised_motif_post')->delete();

        // Define connections based on LLM content analysis
        $connections = [
            ['post_id' => 16, 'localised_motif_id' => 2],
            ['post_id' => 16, 'localised_motif_id' => 4],
            ['post_id' => 18, 'localised_motif_id' => 2],
            ['post_id' => 18, 'localised_motif_id' => 4],
            ['post_id' => 15, 'localised_motif_id' => 2],
            ['post_id' => 36, 'localised_motif_id' => 2],
            ['post_id' => 92, 'localised_motif_id' => 2],
            ['post_id' => 13, 'localised_motif_id' => 2],
            ['post_id' => 22, 'localised_motif_id' => 2],
            ['post_id' => 35, 'localised_motif_id' => 2],
            ['post_id' => 39, 'localised_motif_id' => 2],
            ['post_id' => 66, 'localised_motif_id' => 2],
            ['post_id' => 74, 'localised_motif_id' => 2],
            ['post_id' => 97, 'localised_motif_id' => 2],
            ['post_id' => 63, 'localised_motif_id' => 4],
            ['post_id' => 10, 'localised_motif_id' => 4],
            ['post_id' => 14, 'localised_motif_id' => 4],
            ['post_id' => 37, 'localised_motif_id' => 4],
            ['post_id' => 94, 'localised_motif_id' => 4],
            ['post_id' => 98, 'localised_motif_id' => 4],
            ['post_id' => 1, 'localised_motif_id' => 1],
            ['post_id' => 6, 'localised_motif_id' => 1],
            ['post_id' => 33, 'localised_motif_id' => 1],
            ['post_id' => 34, 'localised_motif_id' => 1],
            ['post_id' => 72, 'localised_motif_id' => 1],
            ['post_id' => 88, 'localised_motif_id' => 1],
            ['post_id' => 90, 'localised_motif_id' => 1],
            ['post_id' => 100, 'localised_motif_id' => 1],
            ['post_id' => 99, 'localised_motif_id' => 1],
            ['post_id' => 4, 'localised_motif_id' => 3],
            ['post_id' => 38, 'localised_motif_id' => 3],
            ['post_id' => 38, 'localised_motif_id' => 5],
            ['post_id' => 45, 'localised_motif_id' => 3],
            ['post_id' => 45, 'localised_motif_id' => 1],
            ['post_id' => 73, 'localised_motif_id' => 3],
            ['post_id' => 73, 'localised_motif_id' => 6],
            ['post_id' => 12, 'localised_motif_id' => 3],
            ['post_id' => 40, 'localised_motif_id' => 3],
            ['post_id' => 65, 'localised_motif_id' => 3],
            ['post_id' => 67, 'localised_motif_id' => 3],
            ['post_id' => 8, 'localised_motif_id' => 5],
            ['post_id' => 17, 'localised_motif_id' => 1],
            ['post_id' => 17, 'localised_motif_id' => 5],
            ['post_id' => 19, 'localised_motif_id' => 5],
            ['post_id' => 20, 'localised_motif_id' => 5],
            ['post_id' => 64, 'localised_motif_id' => 5],
            ['post_id' => 69, 'localised_motif_id' => 5],
            ['post_id' => 89, 'localised_motif_id' => 1],
            ['post_id' => 89, 'localised_motif_id' => 5],
            ['post_id' => 91, 'localised_motif_id' => 5],
            ['post_id' => 95, 'localised_motif_id' => 5],
            ['post_id' => 70, 'localised_motif_id' => 6],
            ['post_id' => 71, 'localised_motif_id' => 6],
            ['post_id' => 93, 'localised_motif_id' => 6],
            ['post_id' => 96, 'localised_motif_id' => 1],
            ['post_id' => 96, 'localised_motif_id' => 6],
            ['post_id' => 46, 'localised_motif_id' => 6],
            ['post_id' => 41, 'localised_motif_id' => 6],
            ['post_id' => 47, 'localised_motif_id' => 6],
        ];

        // Insert connections
        foreach ($connections as $connection) {
            DB::table('localised_motif_post')->insert([
                'post_id' => $connection['post_id'],
                'localised_motif_id' => $connection['localised_motif_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $this->command->info('Successfully connected ' . count($connections) . ' post-motif relationships.');
        
        // Show summary
        $this->showSummary();
    }

    private function showSummary(): void
    {
        $motifs = [
            1 => 'Heraklion',
            2 => 'Heraklion Airport',
            3 => 'Chania',
            4 => 'Chania Airport',
            5 => 'Rethymno',
            6 => 'Agios Nikolaos'
        ];

        $this->command->info('\n=== CONNECTION SUMMARY ===');
        foreach ($motifs as $id => $name) {
            $count = DB::table('localised_motif_post')
                ->where('localised_motif_id', $id)
                ->count();
            $this->command->line("$name: $count posts");
        }
    }
}
