version: '3.5'
services:
  rentals:
    container_name: www.rentals.local
    build:
      context: .
      dockerfile: docker/rentals/Dockerfile
      args:
        - PUID=1000
    volumes:
      - rentals_cache:/var/www/.composer:rw # composer cache
      - .:/var/www/html:rw
      - ./docker/rentals/prepare-container.sh:/dde/prepare-container.sh # runtime preparations after volumes have been mounted
    depends_on:
      - rentals_db
    networks:
      - n1
    environment:
      - VIRTUAL_HOST=rentals.local,www.rentals.local,cretanrentals.local,www.cretanrentals.local
      - VIRTUAL_PORT=80
      - COMPOSER_MEMORY_LIMIT=-1
    entrypoint: /usr/local/bin/custom-entrypoint.sh
  rentals_db:
    build:
      context: .
      dockerfile: docker/db/Dockerfile
    container_name: db.rentals.local
    platform: 'linux/x86_64'
    environment:
      - MYSQL_ROOT_PASSWORD=0
      - MYSQL_DATABASE=rentals
      - VIRTUAL_HOST=db.rentals.local
      - VIRTUAL_PORT=3306
      - TZ=Europe/Athens
    networks:
      - n1
    volumes:
      - rentals_db_data:/var/lib/mysql
      - ./docker/db/conf/mysqld.cnf:/etc/mysql/mysql.conf.d/mysqld.cnf # runtime add conf file
    ports:
      - "33338:3306"
  rentals_proxy:
    image: jwilder/nginx-proxy
    container_name: proxy.rentals.local
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/proxy/nginx.conf:/etc/nginx/nginx.conf
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - n1
    logging:
      driver: "json-file"
      options:
        max-size: "2m"
networks:
  n1:
    name: dockerdevenv_default
volumes:
  rentals_cache:
  rentals_db_data:

