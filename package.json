{"private": true, "scripts": {"dev": "vite", "mix": "npm run mix-watch", "mix-watch": "mix watch", "mix-prod": "mix --production", "build": "vite build", "build-all": "vite build && npm run mix-prod", "hot": "vite", "watch": "npm-run-all --parallel mix-watch dev"}, "devDependencies": {"autoprefixer": "^10.4.20", "axios": "^1.7.9", "bootstrap-sass": "^3.4.1", "cross-env": "^5.2.1", "jquery": "^3.5.1", "jquery-ui": "^1.12.1", "laravel-mix": "^6.0.49", "laravel-vite-plugin": "^1.2.0", "lodash": "^4.17.15", "npm-run-all": "^4.1.5", "postcss": "^8.5.1", "resolve-url-loader": "^5.0.0", "sass": "^1.56.2", "sass-loader": "^16.0.4", "tailwindcss": "^3.4.17", "vite": "^6.0.11", "vue": "^3.5.13", "webpack-dev-server": "^5.2.0"}, "dependencies": {"flatpickr": "^4.6.13", "magnific-popup": "^1.1.0", "node-sass": "^9.0.0", "popper.js": "^1.12.9", "slick-carousel": "^1.8.1"}}