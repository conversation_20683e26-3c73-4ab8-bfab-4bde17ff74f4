{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "anhskohbo/no-captcha": "*", "artesaos/seotools": "*", "astrotomic/laravel-translatable": "*", "cviebrock/eloquent-sluggable": "*", "cyrildewit/eloquent-viewable": "^7.0", "deeplcom/deepl-php": "^1.9", "doctrine/dbal": "^3.6", "google/apiclient": "^2.15", "guzzlehttp/guzzle": "^7.2", "jeroennoten/laravel-adminlte": "*", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "laravel/ui": "*", "laravelcollective/html": "*", "livewire/livewire": "^3.2", "mcamara/laravel-localization": "*", "nwidart/laravel-modules": "^10.0", "opcodesio/log-viewer": "*", "spatie/laravel-medialibrary": "*", "spatie/laravel-searchable": "^1.12", "spatie/laravel-sitemap": "*", "spatie/schema-org": "*", "staudenmeir/eloquent-has-many-deep": "^1.7", "torann/geoip": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}