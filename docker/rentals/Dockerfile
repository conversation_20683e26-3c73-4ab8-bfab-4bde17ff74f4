FROM php:8.1-apache

# Install Dependencies.
RUN apt-get update && apt-get install -y --no-install-recommends libmcrypt-dev libxml2-dev zlib1g-dev unzip vim less sudo imagemagick libpng-dev libjpeg62-turbo-dev \
    gcc make autoconf libc-dev pkg-config libmcrypt-dev libonig-dev libzip-dev --fix-missing
#RUN apt-get update && apt-get install -y --no-install-recommends pecl install mcrypt-1.0.1 && \
    # Enable PHP extensions.
RUN docker-php-ext-configure gd --with-jpeg=/usr/include/ && \
    docker-php-ext-install mbstring pdo pdo_mysql exif xml zip gd && \
    # Install Composer.
    curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer && \
    # Install xdebug
    pecl install xdebug \
        && docker-php-ext-enable xdebug \
        && echo "xdebug.mode=debug" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
        && echo "xdebug.client_host = host.docker.internal" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \

    # Clean up.
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/

ARG PUID=1000
ENV PUID ${PUID}
RUN usermod -u ${PUID} www-data

RUN mkdir -p /var/www/html /dde/ && \
# Give permissions to www-data user
    chown -R www-data /var/www/ /dde/

# Expose 443 port for HTTPS.
EXPOSE 80 443

# Set default work directory.
WORKDIR /var/www/html

COPY ./docker/rentals/custom-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/custom-entrypoint.sh
# change www-uid to prevent files being created with root permissions on host machine.

# Set up apache
COPY docker/rentals/rentals.conf /etc/apache2/sites-available/rentals.conf
RUN a2ensite rentals.conf
RUN a2enmod rewrite


