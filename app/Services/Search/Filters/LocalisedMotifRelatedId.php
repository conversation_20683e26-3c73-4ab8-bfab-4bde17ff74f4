<?php

namespace App\Services\Search\Filters;

use Illuminate\Database\Eloquent\Builder;

class LocalisedMotifRelatedId extends GlobalFilter
{
    /**
     * Filter key to use in query (relationship name)
     *
     * @var string
     */
    protected static string $filter_key = 'localisedMotifs';

    /**
     * Table name to use in WHERE clause
     * Custom implementation for the case of having a snake_case field name
     *
     * @var string
     */
    protected static string $table_name = 'localised_motifs';

    /**
     * Apply a given search value to the builder instance.
     *
     * @param Builder $builder
     * @param mixed $value
     * @return Builder $builder
     */
    public static function apply(Builder $builder, $value): Builder
    {
        return $builder->whereHas(static::$filter_key, function (Builder $query) use ($value) {
            $query->where(static::$table_name . '.id', '=', $value);
        });
    }
}
